'use client';

import { <PERSON>aPhone, FaWhatsapp, FaClock, FaShieldAlt, FaTools, FaWater, FaWrench, FaBuilding } from "react-icons/fa";
import Link from "next/link";
import Image from "next/image";
import { Fade } from "@/components/animations/Fade";
import { EmergencyContactForm } from "@/components/forms/EmergencyContactForm";
import { Translations } from "@/types/translations";
import { useState, useEffect } from "react";

interface EmergencyClientProps {
  data: any;
  t: Translations;
  lang: string;
}

export function EmergencyClient({ data, t, lang }: EmergencyClientProps) {
  // Preveniamo errori di idratazione con un hook di mounting
  const [isMounted, setIsMounted] = useState(false);
  
  useEffect(() => {
    setIsMounted(true);
  }, []);
  
  // Se il componente non è ancora montato, rendiamo un contenitore vuoto
  // con la stessa struttura di base per evitare errori di idratazione
  if (!isMounted) {
    return <main className="min-h-screen bg-white">
      <section className="relative h-[90vh] min-h-[600px] overflow-hidden bg-gradient-to-br from-inparoblue-900 via-inparoblue-700 to-inparoblue-500 pt-28 flex items-center"></section>
    </main>;
  }
  
  return (
    <main className="min-h-screen bg-white">
      {/* Hero Section - Design moderno con gradienti */}
      <section className="relative h-[90vh] min-h-[600px] overflow-hidden bg-gradient-to-br from-inparoblue-900 via-inparoblue-700 to-inparoblue-500 pt-28 flex items-center">
        {/* Elementi decorativi animati */}
        <div className="absolute top-20 left-10 w-20 h-20 rounded-full bg-white opacity-5 animate-pulse"></div>
        <div className="absolute bottom-40 right-20 w-32 h-32 rounded-full bg-inparoblue-300 opacity-10 animate-pulse animation-delay-1000"></div>
        <div className="absolute top-1/3 right-1/4 w-24 h-24 rounded-full bg-inparoblue-400 opacity-5 animate-pulse animation-delay-2000"></div>
        
        {/* Elemento di design - forma geometrica */}
        <div className="absolute -bottom-20 -left-20 w-80 h-80 rounded-full border-8 border-white opacity-5 transform rotate-45"></div>
        <div className="absolute -top-10 -right-10 w-60 h-60 border-4 border-white opacity-5"></div>
        
        {/* Linea decorativa */}
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white to-transparent opacity-20"></div>
        
        <div className="relative mx-auto max-w-7xl px-6 lg:px-8 h-full flex flex-col justify-center z-10">
          <div className="mx-auto max-w-3xl text-center">
            <Fade>
              <div className="mb-6 inline-flex items-center justify-center rounded-full bg-inparoblue-900 bg-opacity-20 px-4 py-1 text-sm font-medium text-white ring-1 ring-inset ring-white/20">
                <span className="mr-2 h-2 w-2 rounded-full bg-inparoblue-400 animate-pulse"></span>
                {t.emergency?.emergency24_7 || "Emergenza 24/7"}
              </div>
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-manrope font-bold tracking-tight text-white drop-shadow-md px-2 md:px-4 lg:px-6 break-words hyphens-auto max-w-full mb-2">
                {data.hero.title}
              </h1>
              <div className="h-1 w-24 mx-auto bg-white opacity-50 my-4 rounded-full"></div>
              <p className="mt-6 text-base md:text-lg font-sans leading-relaxed text-white text-opacity-90 px-4 sm:px-0">
                {data.hero.subtitle}
              </p>
            </Fade>
            <Fade>
              <div className="mt-8 flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-x-6">
                <Link
                  href={`tel:${data.cta.emergency_number}`}
                  className="w-full sm:w-auto rounded-xl bg-white px-8 py-4 text-xl font-semibold text-inparoblue-700 shadow-lg hover:bg-inparoblue-50 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-inparoblue-600 flex items-center justify-center gap-2 transition-all duration-300 hover:scale-105 relative overflow-hidden group"
                >
                  <span className="absolute inset-0 bg-inparoblue-100 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></span>
                  <FaPhone className="h-5 w-5" />
                  <span>{data.cta.emergency_number}</span>
                </Link>
                <Link
                  href={`https://wa.me/${data.cta.whatsapp_number.replace(/\s+/g, '')}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-full sm:w-auto rounded-xl bg-white bg-opacity-10 border border-white border-opacity-20 backdrop-blur-sm px-8 py-4 text-xl font-semibold text-white shadow-lg hover:bg-opacity-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-white flex items-center justify-center gap-2 transition-all duration-300 hover:scale-105"
                >
                  <FaWhatsapp className="h-5 w-5" />
                  <span>{t.emergency?.whatsapp || "Whatsapp"}</span>
                </Link>
              </div>
            </Fade>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <Fade>
            <div className="mx-auto max-w-2xl text-center">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                {data.features.title}
              </h2>
            </div>
          </Fade>
          <div className="mx-auto mt-16 max-w-7xl">
            <dl className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
              <Fade>
                <div className="rounded-xl bg-white p-8 shadow-lg ring-1 ring-gray-200 transition-all duration-300 hover:shadow-xl">
                  <dt className="flex items-center gap-x-3">
                    <FaClock className="h-6 w-6 text-blue-600" />
                    <span className="text-lg font-semibold leading-7 text-gray-900">
                      {data.features.feature1.title}
                    </span>
                  </dt>
                  <dd className="mt-4 text-base leading-7 text-gray-600">
                    {data.features.feature1.description}
                  </dd>
                </div>
              </Fade>
              <Fade>
                <div className="rounded-xl bg-white p-8 shadow-lg ring-1 ring-gray-200 transition-all duration-300 hover:shadow-xl">
                  <dt className="flex items-center gap-x-3">
                    <FaShieldAlt className="h-6 w-6 text-blue-600" />
                    <span className="text-lg font-semibold leading-7 text-gray-900">
                      {data.features.feature2.title}
                    </span>
                  </dt>
                  <dd className="mt-4 text-base leading-7 text-gray-600">
                    {data.features.feature2.description}
                  </dd>
                </div>
              </Fade>
              <Fade>
                <div className="rounded-xl bg-white p-8 shadow-lg ring-1 ring-gray-200 transition-all duration-300 hover:shadow-xl">
                  <dt className="flex items-center gap-x-3">
                    <FaTools className="h-6 w-6 text-blue-600" />
                    <span className="text-lg font-semibold leading-7 text-gray-900">
                      {data.features.feature3.title}
                    </span>
                  </dt>
                  <dd className="mt-4 text-base leading-7 text-gray-600">
                    {data.features.feature3.description}
                  </dd>
                </div>
              </Fade>
            </dl>
          </div>
        </div>
      </section>

      {/* Services Section - Design Moderno */}
      <section className="bg-gradient-to-b from-white via-gray-50 to-inparoblue-50 py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8 relative">
          {/* Elementi decorativi */}
          <div className="absolute -top-10 right-10 w-32 h-32 bg-inparoblue-50 rounded-full opacity-70 blur-3xl"></div>
          <div className="absolute bottom-40 left-10 w-48 h-48 bg-inparoblue-50 rounded-full opacity-70 blur-3xl"></div>
          
          <Fade>
            <div className="mx-auto max-w-2xl text-center mb-16">

              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                {data.services_title || (t.emergency?.services_title || "Services")}
              </h2>
              <div className="h-1 w-24 mx-auto bg-blue-500 opacity-70 my-6 rounded-full"></div>
            </div>
          </Fade>

          <div className="mt-10 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {data.services.map((service: any) => (
              <Fade key={service.id}>
                <div className="relative group overflow-hidden rounded-2xl bg-white ring-1 ring-gray-200 transition-all duration-300 hover:shadow-xl">
                  {/* Elementi decorativi */}
                  <div className="absolute -top-4 -right-4 w-16 h-16 bg-blue-100 rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                  
                  <div className="p-8">
                    <div className="flex items-center">
                      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-50 mr-4">
                        {service.id === 1 && <FaWater className="h-6 w-6 text-inparoblue-700" />}
                        {service.id === 2 && <FaWrench className="h-6 w-6 text-inparoblue-700" />}
                        {service.id === 3 && <FaBuilding className="h-6 w-6 text-inparoblue-700" />}
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900">{service.title}</h3>
                    </div>
                    
                    <p className="mt-4 text-sm text-gray-600">{service.description}</p>

                  </div>
                </div>
              </Fade>
            ))}
          </div>
        </div>
      </section>

      {/* Certificate Section */}
      <section className="py-24 sm:py-32 bg-gray-50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <Fade>
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                {t.emergency?.equipment?.certification?.title || "Certificazione Professionale"}
              </h2>
              <p className="mt-6 text-lg leading-8 text-gray-600">
                {t.emergency?.equipment?.certification?.description || "Il nostro team è certificato per la gestione e il risanamento dei danni causati dall'acqua"}
              </p>
            </div>
          </Fade>
          
          <Fade>
            <div className="max-w-5xl mx-auto space-y-8">
              {/* First Certificate */}
              <div className="bg-white rounded-xl shadow-xl overflow-hidden border border-gray-200">
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="p-6 flex flex-col justify-center">
                    <h3 className="text-xl font-bold text-gray-900 mb-3">
                      {t.emergency?.equipment?.certification?.certificate?.title || "Certificato Fachberater Wasserschadensanierung"}
                    </h3>
                    <p className="text-gray-600 mb-6">
                      {t.emergency?.equipment?.certification?.certificate?.description || "Rilasciato da Dantherm Academy"}
                    </p>
                    <p className="text-gray-600 mb-8">
                      Questo certificato attesta la nostra competenza specialistica nel trattamento dei danni da acqua, garantendo interventi professionali secondo gli standard più elevati del settore.
                    </p>
                    <div className="mt-auto">
                      <a
                        href="/documents/certificato.pdf"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-5 py-2.5 bg-inparoblue-600 text-white rounded-lg hover:bg-inparoblue-700 transition-colors duration-300 shadow-md">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        {t.emergency?.equipment?.certification?.certificate?.viewCertificate || "Visualizza certificato"}
                      </a>
                    </div>
                  </div>

                  <div className="bg-gray-50 flex items-center justify-center p-4 md:p-6">
                    <a href="/documents/certificato.pdf" target="_blank" rel="noopener noreferrer" className="block relative overflow-hidden group max-h-96">
                      <Image
                        src="/images/certificates/certificato.png"
                        alt="Certificato Dantherm Academy"
                        width={300}
                        height={400}
                        className="object-contain rounded-lg shadow-md border border-gray-200 transition-transform duration-300 group-hover:scale-105"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                        <div className="p-4 w-full">
                          <p className="text-white text-sm font-medium text-center">Clicca per visualizzare</p>
                        </div>
                      </div>
                    </a>
                  </div>
                </div>
              </div>

              {/* Second Certificate - Building Drying */}
              <div className="bg-white rounded-xl shadow-xl overflow-hidden border border-gray-200">
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="p-6 flex flex-col justify-center">
                    <h3 className="text-xl font-bold text-gray-900 mb-3">
                      Certificato Bautrocknungen
                    </h3>
                    <p className="text-gray-600 mb-6">
                      Certificazione specializzata per servizi di asciugatura edile
                    </p>
                    <p className="text-gray-600 mb-8">
                      Questo certificato attesta la nostra competenza specialistica nei servizi di Bautrocknungen (asciugatura edile), garantendo interventi professionali per il trattamento dell'umidità e l'asciugatura di strutture edili.
                    </p>
                    <div className="mt-auto">
                      <a
                        href="/images/certificates/certificato-2.pdf"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-5 py-2.5 bg-inparoblue-600 text-white rounded-lg hover:bg-inparoblue-700 transition-colors duration-300 shadow-md">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Visualizza certificato Bautrocknungen
                      </a>
                    </div>
                  </div>

                  <div className="bg-gray-50 flex items-center justify-center p-4 md:p-6">
                    <a href="/images/certificates/certificato-2.pdf" target="_blank" rel="noopener noreferrer" className="block relative overflow-hidden group max-h-96">
                      <div className="w-full h-64 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg shadow-md border border-gray-200 transition-transform duration-300 group-hover:scale-105 flex items-center justify-center">
                        <div className="text-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-blue-600 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                          <p className="text-blue-800 font-medium">Certificato Bautrocknungen</p>
                          <p className="text-blue-600 text-sm mt-1">PDF Document</p>
                        </div>
                      </div>
                      <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                        <div className="p-4 w-full">
                          <p className="text-white text-sm font-medium text-center">Clicca per visualizzare</p>
                        </div>
                      </div>
                    </a>
                  </div>
                </div>
              </div>
            </div>
              </div>
            </div>
          </Fade>
        </div>
      </section>

      {/* Trotec Products Section */}
      <section className="py-24 sm:py-32 bg-gradient-to-br from-white via-gray-50 to-inparoblue-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 relative">
          {/* Elementi decorativi */}
          <div className="absolute -top-20 right-0 w-72 h-72 bg-inparoblue-100 rounded-full opacity-70 blur-3xl"></div>
          <div className="absolute bottom-40 left-20 w-80 h-80 bg-inparoblue-100 rounded-full opacity-60 blur-3xl"></div>
          <div className="absolute top-1/2 left-1/3 w-48 h-48 bg-yellow-50 rounded-full opacity-40 blur-2xl"></div>
          
          <Fade>
            <div className="mx-auto max-w-2xl text-center mb-12 sm:mb-16">
              <div className="inline-block bg-blue-50 px-6 py-2 rounded-full mb-3">
                <span className="text-sm font-semibold text-blue-700">{data.trotec?.subtitle}</span>
              </div>
              <h2 className="text-3xl sm:text-4xl font-bold tracking-tight text-gray-900 lg:text-5xl relative inline-block">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-inparoblue-700 to-inparoblue-900">{data.trotec.title}</span>
                <div className="absolute -bottom-3 left-0 right-0 h-2 bg-gradient-to-r from-inparoblue-600 to-inparoblue-500 rounded-full"></div>
              </h2>
              <p className="mt-6 text-base sm:text-lg leading-7 sm:leading-8 text-gray-700 max-w-xl mx-auto">
                {data.trotec.description}
              </p>
            </div>
          </Fade>

          <div className="mt-12 sm:mt-16 space-y-12">
            {data.trotec.products.map((product: any, index: number) => (
              <Fade key={product.id}>
                <div className="relative bg-white rounded-2xl p-4 sm:p-6 shadow-lg hover:shadow-xl transition-all duration-300 border-b-3 border-blue-500 overflow-hidden"> 
                  {/* Sfondo decorativo */}
                  <div className="absolute top-0 right-0 -mt-6 -mr-6 w-24 h-24 bg-gradient-to-br from-inparoblue-200 to-inparoblue-200 rounded-full opacity-70 blur-xl"></div>
                  <div className="absolute bottom-0 left-0 -mb-3 -ml-3 w-16 h-16 bg-gradient-to-br from-yellow-100 to-inparoblue-100 rounded-full opacity-60 blur-xl"></div>
                  
                  <div className="flex flex-col md:flex-row md:items-start gap-6 md:gap-8 relative z-10">
                    {/* Intestazione card (visibile su tutti i dispositivi) */}
                    <div className="flex items-center mb-4 md:hidden">
                      <span className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-inparoblue-700 to-inparoblue-500 text-white text-base sm:text-lg font-bold rounded-lg mr-3 shadow-md">{product.id}</span>
                      <h3 className="text-lg sm:text-xl font-bold text-gray-900">{product.title}</h3>
                    </div>
                    
                    {/* Contenuto della card */}
                    <div className="w-full md:w-1/2 flex flex-col">
                      {/* Intestazione card (visibile solo su desktop) */}
                      <div className="hidden md:flex items-center mb-4">
                        <span className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-inparoblue-700 to-inparoblue-500 text-white text-base sm:text-lg font-bold rounded-lg mr-3 shadow-md">{product.id}</span>
                        <h3 className="text-lg sm:text-2xl font-bold text-gray-900">{product.title}</h3>
                      </div>
                      <p className="text-gray-700 text-sm sm:text-base mb-4 leading-relaxed">{product.description}</p>
                      <div className="w-24 h-1 bg-gradient-to-r from-inparoblue-500 to-inparoblue-400 rounded-full mt-auto"></div>
                    </div>
                    
                    {/* Parte visuale/tecnica della card */}
                    <div className="w-full md:w-1/2 bg-gradient-to-br from-gray-50 to-inparoblue-50 rounded-xl p-4 sm:p-5 shadow-md border border-inparoblue-100">
                      {product.id === 1 && (
                        <div className="flex flex-col items-center">
                          <div className="w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-inparoblue-100 to-inparoblue-300 rounded-full flex items-center justify-center mb-4 shadow-md transform hover:scale-105 transition-transform duration-300">
                            <FaWater className="w-10 h-10 sm:w-12 sm:h-12 text-inparoblue-700" />
                          </div>
                          <div className="text-center w-full">
                            <div className="font-bold text-gray-900 text-lg mb-3">TWP 11025 E</div>
                            <div className="grid grid-cols-2 gap-2 sm:gap-3">
                              <div className="bg-white p-2 rounded-lg shadow-sm border-l-3 border-blue-500 transform hover:-translate-y-1 transition-transform duration-300">
                                <div className="font-semibold text-blue-800 text-xs sm:text-sm">
                                  {t.emergency?.flowRate || "Portata"}
                                </div>
                                <div className="text-gray-900 text-sm">15.000 l/h</div>
                              </div>
                              <div className="bg-white p-2 rounded-lg shadow-sm border-l-3 border-blue-500 transform hover:-translate-y-1 transition-transform duration-300">
                                <div className="font-semibold text-blue-800 text-xs sm:text-sm">
                                  {t.emergency?.power || "Potenza"}
                                </div>
                                <div className="text-gray-900 text-sm">1.100 W</div>
                              </div>
                              <div className="bg-white p-2 rounded-lg shadow-sm border-l-3 border-blue-500 transform hover:-translate-y-1 transition-transform duration-300">
                                <div className="font-semibold text-blue-800 text-xs sm:text-sm">
                                  {t.emergency?.depth || "Profondità"}
                                </div>
                                <div className="text-gray-900 text-sm">10 m</div>
                              </div>
                              <div className="bg-white p-2 rounded-lg shadow-sm border-l-3 border-blue-500 transform hover:-translate-y-1 transition-transform duration-300">
                                <div className="font-semibold text-blue-800 text-xs sm:text-sm">
                                  {t.emergency?.head || "Testa"}
                                </div>
                                <div className="text-gray-900 text-sm">11 m</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                      
                      {product.id === 2 && (
                        <div className="flex flex-col items-center">
                          <div className="w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-inparoblue-100 to-inparoblue-300 rounded-full flex items-center justify-center mb-4 shadow-md transform hover:scale-105 transition-transform duration-300">
                            <FaWrench className="w-10 h-10 sm:w-12 sm:h-12 text-inparoblue-700" />
                          </div>
                          <div className="text-center w-full">
                            <div className="font-bold text-gray-900 text-lg mb-3">MultiQube System</div>
                            <div className="flex flex-col gap-2">
                              <div className="bg-white p-2 rounded-lg shadow-sm border-l-3 border-blue-500 flex items-center transform hover:-translate-x-1 transition-transform duration-300">
                                <div className="w-2 h-2 rounded-full bg-blue-600 mr-2 flex-shrink-0"></div>
                                <div className="text-xs sm:text-sm text-gray-800">
                                  {t.emergency?.fastDrying || "Asciugatura rapida"}
                                </div>
                              </div>
                              <div className="bg-white p-2 rounded-lg shadow-sm border-l-3 border-blue-500 flex items-center transform hover:-translate-x-1 transition-transform duration-300">
                                <div className="w-2 h-2 rounded-full bg-blue-600 mr-2 flex-shrink-0"></div>
                                <div className="text-xs sm:text-sm text-gray-800">
                                  {t.emergency?.moldPrevention || "Prevenzione muffa"}
                                </div>
                              </div>
                              <div className="bg-white p-2 rounded-lg shadow-sm border-l-3 border-blue-500 flex items-center transform hover:-translate-x-1 transition-transform duration-300">
                                <div className="w-2 h-2 rounded-full bg-blue-600 mr-2 flex-shrink-0"></div>
                                <div className="text-xs sm:text-sm text-gray-800">
                                  {t.emergency?.advancedDehumidification || "Deumidificazione avanzata"}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                      
                      {product.id === 3 && (
                        <div className="flex flex-col items-center">
                          <div className="w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-inparoblue-100 to-inparoblue-300 rounded-full flex items-center justify-center mb-4 shadow-md transform hover:scale-105 transition-transform duration-300">
                            <FaBuilding className="w-10 h-10 sm:w-12 sm:h-12 text-inparoblue-700" />
                          </div>
                          <div className="text-center w-full">
                            <div className="font-bold text-gray-900 text-lg mb-3">{t.emergency?.markingDyes || "Coloranti di marcatura"}</div>
                            <div className="flex flex-col gap-2">
                              <div className="bg-white p-2 rounded-lg shadow-sm border-l-3 border-blue-500 flex items-center transform hover:-translate-x-1 transition-transform duration-300">
                                <div className="w-2 h-2 rounded-full bg-blue-600 mr-2 flex-shrink-0"></div>
                                <div className="text-xs sm:text-sm text-gray-800">
                                  {t.emergency?.leakDetection || "Rilevamento perdite"}
                                </div>
                              </div>
                              <div className="bg-white p-2 rounded-lg shadow-sm border-l-3 border-blue-500 flex items-center transform hover:-translate-x-1 transition-transform duration-300">
                                <div className="w-2 h-2 rounded-full bg-blue-600 mr-2 flex-shrink-0"></div>
                                <div className="text-xs sm:text-sm text-gray-800">
                                  {t.emergency?.quickIdentification || "Identificazione rapida"}
                                </div>
                              </div>
                              <div className="bg-white p-2 rounded-lg shadow-sm border-l-3 border-blue-500 flex items-center transform hover:-translate-x-1 transition-transform duration-300">
                                <div className="w-2 h-2 rounded-full bg-blue-600 mr-2 flex-shrink-0"></div>
                                <div className="text-xs sm:text-sm text-gray-800">
                                  {t.emergency?.optimalVisibility || "Visibilità ottimale"}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </Fade>
            ))}
          </div>
          
          <Fade>
            <div className="mt-20 text-center">
              <p className="text-base sm:text-lg text-gray-700 mb-8 max-w-2xl mx-auto">
                {data.trotec.conclusion}
              </p>
              <div className="inline-flex">
                <Link href="/contact" 
                   className="relative inline-flex items-center px-6 py-3 overflow-hidden text-white bg-gradient-to-r from-inparoblue-600 to-inparoblue-700 rounded-lg group gap-2 shadow-md hover:shadow-lg transition-all duration-300">
                  <span className="absolute right-0 flex items-center justify-start w-10 h-10 duration-300 transform translate-x-full group-hover:translate-x-0">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                    </svg>
                  </span>
                  <span className="text-sm sm:text-base font-medium transition-all duration-300 group-hover:mr-4">
                    {t.emergency?.contactUs || "Contattaci"}
                  </span>
                </Link>
              </div>
            </div>
          </Fade>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-24 sm:py-32 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8 relative">
          
          <Fade>
            <div className="mx-auto max-w-2xl text-center mb-16 relative z-10">
              <h2 className="text-3xl font-bold tracking-tight text-gray-900">
                {t.emergency?.quickProcess || "Processo rapido"}
              </h2>
              <p className="mt-6 text-base leading-8 text-gray-600">
                {data.process_title}
              </p>
            </div>
          </Fade>

          <div className="mx-auto mt-16 max-w-3xl">
            <ol className="relative border-l border-blue-200 space-y-16 mx-auto">
              {data.process.map((step: any) => (
                <Fade key={step.step}>
                  <li className="ml-8">
                    <div className="absolute -left-6 flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-inparoblue-600 to-inparoblue-500 shadow-md">
                      <span className="text-xl font-semibold text-white">{step.step}</span>
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900">{step.title}</h3>
                    <p className="mt-2 text-sm leading-6 text-gray-600">{step.description}</p>
                  </li>
                </Fade>
              ))}
            </ol>
          </div>
        </div>
      </section>

      {/* Contact Form */}
      <section className="bg-gray-50 py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="grid grid-cols-1 gap-16 lg:grid-cols-2 items-center">
            <Fade>
              <div>
                <div className="mb-8">
                  <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                    {data.contact_form.title}
                  </h2>
                  <p className="mt-4 text-base leading-8 text-gray-600">
                    {data.contact_form.description}
                  </p>
                </div>
                
                <div className="rounded-xl bg-gradient-to-r from-inparoblue-600 to-inparoblue-700 text-white p-8 shadow-lg">
                  <h3 className="text-xl font-semibold mb-4">
                    {t.emergency?.callNow || "Chiama ora"}
                  </h3>
                  <p className="mb-6">
                    {t.emergency?.immediateAssistance || "Assistenza immediata"}
                  </p>
                  <Link 
                    href={`tel:${data.cta.emergency_number}`}
                    className="inline-flex items-center text-lg font-semibold hover:underline"
                  >
                    <FaPhone className="mr-2" />
                    {data.cta.emergency_number}
                  </Link>
                </div>
              </div>
            </Fade>
            
            <Fade>
              <div>
                <EmergencyContactForm t={t} lang={lang} />
              </div>
            </Fade>
          </div>
        </div>
      </section>

      {/* Call to Action */}
    </main>
  );
}
